import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as cron from 'node-cron';
import { QueueService } from '../queue/queue.service';
import { ScheduledTaskService } from '../scheduled-tasks/scheduled-task.service';

@Injectable()
export class SchedulerService implements OnModuleInit {
  private readonly logger = new Logger(SchedulerService.name);
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();
  private pollingInterval: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly queueService: QueueService,
    private readonly scheduledTaskService: ScheduledTaskService,
  ) {}

  async onModuleInit() {
    this.logger.log('🚀 Scheduler service initialized');
    // Note: startScheduler() will be called from the main bootstrap function
    // to avoid double initialization
  }

  async startScheduler() {
    this.logger.log('🔄 Starting database task polling...');

    // Direct implementation to bypass any issues
    this.logger.log(
      '🔄 Starting frequent polling every 20 seconds for testing...',
    );

    this.pollingInterval = setInterval(async () => {
      const timestamp = new Date().toISOString();
      this.logger.log(
        `🔍 [${timestamp}] Polling database for scheduled tasks...`,
      );

      try {
        const tasksToProcess =
          await this.scheduledTaskService.getTasksReadyForExecution(50);
        this.logger.log(
          `📊 Found ${tasksToProcess.length} tasks in database query`,
        );

        if (tasksToProcess.length === 0) {
          this.logger.log('📭 No tasks ready for execution at this time');
          return;
        }

        this.logger.log(
          `🚀 Found ${tasksToProcess.length} tasks ready for execution`,
        );

        for (const task of tasksToProcess) {
          try {
            await this.queueService.addScheduledTaskJob(
              {
                taskId: task.id,
                type: task.type,
                payload: task.payload,
                attemptNumber: task.attempts + 1,
              },
              {
                priority: task.priority,
                attempts: task.max_attempts,
              },
            );

            this.logger.log(
              `✅ Queued scheduled task: ${task.id} (${task.type}) - ${task.name}`,
            );
          } catch (error) {
            this.logger.error(
              `❌ Failed to queue task ${task.id} (${task.type}):`,
              error,
            );
          }
        }
      } catch (error) {
        this.logger.error('Error in frequent polling:', error);
      }
    }, 60000); // 60 seconds

    this.logger.log('✅ Frequent polling started');
    this.logger.log(
      `✅ Started database polling - checking every 20 seconds for tasks (testing mode)`,
    );
  }

  async stopScheduler() {
    this.logger.log('Stopping scheduled tasks...');

    // Stop the frequent polling interval
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      this.logger.log('Stopped frequent polling interval');
    }

    for (const [name, task] of this.scheduledTasks) {
      task.stop();
      this.logger.log(`Stopped task: ${name}`);
    }

    this.scheduledTasks.clear();
    this.logger.log('All scheduled tasks stopped');
  }
}
