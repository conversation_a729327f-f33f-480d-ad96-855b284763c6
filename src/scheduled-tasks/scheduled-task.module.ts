import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { ScheduledTaskService } from './scheduled-task.service';
import { ScheduledTaskController } from './scheduled-task.controller';
import { TaskExecutionService } from './task-execution.service';
import { BackgroundTaskService } from './background-task.service';
import { TaskHandlerRegistry } from './task-handlers/task-handler.registry';
import { ConsoleLogHandler } from './task-handlers/console-log.handler';
import { DailyNairobiTaskHandler } from './task-handlers/daily-nairobi-task.handler';
import { PrismaModule } from '../prisma/prisma.module';
import { QueueProducerModule } from '../queue/queue-producer.module';

@Module({
  imports: [PrismaModule, forwardRef(() => QueueProducerModule)],
  controllers: [ScheduledTaskController],
  providers: [
    ScheduledTaskService,
    TaskExecutionService,
    BackgroundTaskService,
    TaskHandlerRegistry,
    ConsoleLogHandler,
    DailyNairobiTaskHandler,
  ],
  exports: [
    ScheduledTaskService,
    TaskExecutionService,
    BackgroundTaskService,
    TaskHandlerRegistry,
  ],
})
export class ScheduledTaskModule {}
