{"name": "kb-tracker-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:seed": "ts-node prisma/seed.ts", "db:verify": "ts-node scripts/verify-branches.ts", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:dev": "prisma migrate dev", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio", "start:worker": "node --max-old-space-size=2048 dist/src/worker.js", "start:worker:dev": "node --max-old-space-size=2048 -r ts-node/register src/worker.ts", "start:scheduler": "node --max-old-space-size=2048 dist/src/scheduler.js", "start:scheduler:dev": "node --max-old-space-size=4096 -r ts-node/register src/scheduler.ts", "docker:migrate": "./scripts/docker-migrate.sh migrate", "docker:seed": "./scripts/docker-migrate.sh seed", "docker:reset": "./scripts/docker-migrate.sh reset", "docker:full": "./scripts/docker-migrate.sh full", "docker:studio": "./scripts/docker-migrate.sh studio"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/bullmq": "^11.0.3", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@types/bcrypt": "^6.0.0", "@types/ioredis": "^4.28.10", "@types/multer": "^2.0.0", "@types/node-cron": "^3.0.11", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "bcrypt": "^6.0.0", "bullmq": "^5.58.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cron-parser": "^5.3.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "exceljs": "^4.4.0", "form-data": "^4.0.4", "handlebars": "^4.7.8", "ioredis": "^5.7.0", "nestjs-pino": "^4.4.0", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino": "^9.8.0", "pino-http": "^10.5.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@prisma/client": "^6.12.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^2.0.0", "@types/node": "^22.10.7", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "pg": "^8.16.3", "pino-pretty": "^13.1.1", "prettier": "^3.4.2", "prisma": "^6.12.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}